#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型设置功能集成测试
验证从用户设置到消息发送的完整流程
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


def test_model_setting_flow():
    """测试模型设置的完整流程"""
    print("=== 模型设置功能集成测试 ===")
    
    try:
        # 1. 测试 MessageAsyncRequestContextModelSetting 创建
        print("\n1. 测试模型设置对象创建...")
        from alibabacloud_wuyingaiinner20250708 import models as waiy_models
        
        test_models = ["qwen-plus", "qwen-max", "claude-3.5-sonnet", "gpt-4o"]
        
        for model in test_models:
            model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
                model_level=model
            )
            print(f"   ✓ {model}: {model_setting.model_level}")
        
        # 2. 测试用户设置数据结构
        print("\n2. 测试用户设置数据结构...")
        from src.application.api_models import UserSettingData, CurrentSetting
        
        current_setting = CurrentSetting(
            desktop_id="test_desktop_123",
            model="qwen-max"
        )
        
        setting_data = UserSettingData(
            current_setting=current_setting,
            available_environments=[{"id": "env1", "name": "环境1"}],
            available_models=["qwen-plus", "qwen-max", "claude-3.5-sonnet"],
            version="1.0.0"
        )
        
        print(f"   ✓ 当前设置: desktop_id={setting_data.current_setting.desktop_id}, model={setting_data.current_setting.model}")
        print(f"   ✓ 可用模型: {setting_data.available_models}")
        
        # 3. 测试模型设置逻辑
        print("\n3. 测试模型设置逻辑...")
        
        # 模拟从用户设置获取模型并创建模型设置对象
        model_level = setting_data.current_setting.model
        
        if model_level:
            model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
                model_level=model_level
            )
            print(f"   ✓ 从用户设置创建模型设置: {model_setting.model_level}")
        else:
            print("   ✓ 用户未设置模型，将使用默认配置")
        
        # 4. 测试 waiy_client.create_async_message_context 调用
        print("\n4. 测试消息上下文创建...")
        from src.popclients.waiy_infra_client import create_waiy_infra_client
        
        waiy_client = create_waiy_infra_client()
        
        # 创建模拟的运行时资源
        runtime_resource = waiy_models.MessageAsyncRequestContextRuntimeResource(
            desktop_id="test_desktop",
            auth_code="test_auth_code"
        )
        
        # 创建消息上下文
        message_context = waiy_client.create_async_message_context(
            runtime_resource=runtime_resource,
            session_id="test_session_123",
            user_id="12345",
            model_setting=model_setting
        )
        
        print(f"   ✓ 消息上下文创建成功")
        print(f"   ✓ session_id: {message_context.session_id}")
        print(f"   ✓ user_id: {message_context.user_id}")
        print(f"   ✓ model_setting: {message_context.model_setting.model_level if message_context.model_setting else 'None'}")
        
        # 5. 验证模型设置传递
        print("\n5. 验证模型设置传递...")
        if message_context.model_setting:
            assert message_context.model_setting.model_level == model_level
            print(f"   ✓ 模型设置正确传递: {message_context.model_setting.model_level}")
        else:
            print("   ✗ 模型设置未正确传递")
            return False
        
        print("\n=== 集成测试通过 ===")
        return True
        
    except Exception as e:
        print(f"\n✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 边界情况测试 ===")
    
    try:
        from alibabacloud_wuyingaiinner20250708 import models as waiy_models
        from src.popclients.waiy_infra_client import create_waiy_infra_client
        
        waiy_client = create_waiy_infra_client()
        
        # 1. 测试 model_level 为 None 的情况
        print("\n1. 测试 model_level 为 None...")
        model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
            model_level=None
        )
        
        message_context = waiy_client.create_async_message_context(
            session_id="test_session",
            user_id="12345",
            model_setting=model_setting
        )
        
        print(f"   ✓ model_level=None 处理成功: {message_context.model_setting.model_level}")
        
        # 2. 测试 model_setting 为 None 的情况
        print("\n2. 测试 model_setting 为 None...")
        message_context = waiy_client.create_async_message_context(
            session_id="test_session",
            user_id="12345",
            model_setting=None
        )
        
        print(f"   ✓ model_setting=None 处理成功: {message_context.model_setting}")
        
        # 3. 测试空字符串模型
        print("\n3. 测试空字符串模型...")
        model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
            model_level=""
        )
        
        message_context = waiy_client.create_async_message_context(
            session_id="test_session",
            user_id="12345",
            model_setting=model_setting
        )
        
        print(f"   ✓ model_level='' 处理成功: '{message_context.model_setting.model_level}'")
        
        print("\n=== 边界情况测试通过 ===")
        return True
        
    except Exception as e:
        print(f"\n✗ 边界情况测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始模型设置功能集成测试...\n")
    
    test_results = []
    
    # 运行测试
    test_results.append(test_model_setting_flow())
    test_results.append(test_edge_cases())
    
    # 汇总结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 最终测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有集成测试通过！模型设置功能实现正确。")
        return True
    else:
        print("❌ 部分集成测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
