#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 AgentBay 云资源ID 配置功能
"""

import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.domain.services.session_service import SessionService
from src.domain.services.auth_service import AuthContext
from src.shared.config.environments import env_manager


def test_agentbay_config_loading():
    """测试 AgentBay 配置加载"""
    print("=== 测试 AgentBay 配置加载 ===")
    
    try:
        # 获取当前环境配置
        config = env_manager.get_config()
        current_env = env_manager.current_env.value
        
        print(f"\n当前环境: {current_env}")
        
        # 测试配置项是否存在
        agentbay_cloud_resource_id = config._get_config_value('agentbay_cloud_resource_id')
        print(f"AgentBay 云资源ID: {agentbay_cloud_resource_id}")
        
        # 验证配置
        if agentbay_cloud_resource_id:
            print(f"✓ 配置加载成功")
            
            # 验证不同环境的配置值
            if current_env == "daily":
                expected_id = "akm-2f6c64e0-ba79-47cb-9847-d265773c7873"
                assert agentbay_cloud_resource_id == expected_id, f"Daily 环境配置不匹配: 期望 {expected_id}, 实际 {agentbay_cloud_resource_id}"
                print(f"✓ Daily 环境配置验证通过")
            elif current_env == "pre":
                expected_id = "akm-pre-2f6c64e0-ba79-47cb-9847-d265773c7873"
                assert agentbay_cloud_resource_id == expected_id, f"Pre 环境配置不匹配: 期望 {expected_id}, 实际 {agentbay_cloud_resource_id}"
                print(f"✓ Pre 环境配置验证通过")
            elif current_env == "prod":
                expected_id = "akm-prod-2f6c64e0-ba79-47cb-9847-d265773c7873"
                assert agentbay_cloud_resource_id == expected_id, f"Prod 环境配置不匹配: 期望 {expected_id}, 实际 {agentbay_cloud_resource_id}"
                print(f"✓ Prod 环境配置验证通过")
            else:
                print(f"⚠️  未知环境: {current_env}")
        else:
            print(f"✗ 配置项不存在或为空")
            return False
        
        return True
        
    except Exception as e:
        print(f"\n✗ 配置加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_session_service_agentbay_config():
    """测试 SessionService 中的 AgentBay 配置使用"""
    print("\n=== 测试 SessionService AgentBay 配置使用 ===")
    
    try:
        # 创建 SessionService 实例
        session_service = SessionService()
        
        # 创建模拟的认证上下文
        mock_context = Mock(spec=AuthContext)
        mock_context.user_key = "test_user"
        mock_context.end_user_id = 12345
        
        # 测试 AgentBay 运行时资源构建
        print("\n1. 测试 AgentBay 运行时资源构建...")
        
        runtime_resource = session_service._build_runtime_resource(
            desktop_id="Agentbay",
            auth_code="test_auth_code",
            auth_context=mock_context
        )
        
        print(f"   ✓ 类型: {runtime_resource.type}")
        print(f"   ✓ 云资源ID: {runtime_resource.cloud_resource_id}")
        print(f"   ✓ 区域: {runtime_resource.region}")
        print(f"   ✓ Token: {runtime_resource.token}")
        
        # 验证结果
        assert runtime_resource.type == "agentbay", f"类型应该是 agentbay，实际是 {runtime_resource.type}"
        
        # 验证云资源ID来自配置
        config = env_manager.get_config()
        expected_cloud_resource_id = config._get_config_value('agentbay_cloud_resource_id', 'akm-2f6c64e0-ba79-47cb-9847-d265773c7873')
        assert runtime_resource.cloud_resource_id == expected_cloud_resource_id, f"云资源ID应该是 {expected_cloud_resource_id}，实际是 {runtime_resource.cloud_resource_id}"
        
        print(f"   ✓ 验证通过：云资源ID来自配置文件")
        
        # 测试不同大小写的 AgentBay
        test_cases = ["Agentbay", "agentbay", "AGENTBAY", "AgentBay"]
        
        print(f"\n2. 测试不同大小写的 AgentBay...")
        for desktop_id in test_cases:
            runtime_resource = session_service._build_runtime_resource(
                desktop_id=desktop_id,
                auth_code="test_auth_code",
                auth_context=mock_context
            )
            
            assert runtime_resource.type == "agentbay", f"桌面ID {desktop_id} 应该识别为 agentbay 类型"
            assert runtime_resource.cloud_resource_id == expected_cloud_resource_id, f"桌面ID {desktop_id} 的云资源ID不正确"
            
            print(f"   ✓ {desktop_id}: 正确识别为 agentbay 类型")
        
        return True
        
    except Exception as e:
        print(f"\n✗ SessionService AgentBay 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_environment_switching():
    """测试环境切换时的配置变化"""
    print("\n=== 测试环境切换配置 ===")
    
    try:
        from src.shared.config.environments import Environment
        
        # 保存当前环境
        original_env = env_manager.current_env
        
        # 测试不同环境的配置
        test_environments = [
            (Environment.DAILY, "akm-2f6c64e0-ba79-47cb-9847-d265773c7873"),
            (Environment.PRE, "akm-pre-2f6c64e0-ba79-47cb-9847-d265773c7873"),
            (Environment.PROD, "akm-prod-2f6c64e0-ba79-47cb-9847-d265773c7873"),
        ]
        
        for env, expected_id in test_environments:
            print(f"\n测试 {env.value} 环境...")
            
            # 切换环境
            env_manager.switch_environment(env)
            
            # 获取配置
            config = env_manager.get_config()
            agentbay_id = config._get_config_value('agentbay_cloud_resource_id')
            
            print(f"   环境: {env.value}")
            print(f"   AgentBay ID: {agentbay_id}")
            print(f"   期望 ID: {expected_id}")
            
            # 验证配置
            assert agentbay_id == expected_id, f"{env.value} 环境的 AgentBay ID 不匹配"
            print(f"   ✓ {env.value} 环境配置验证通过")
            
            # 测试 SessionService 是否使用了正确的配置
            session_service = SessionService()
            runtime_resource = session_service._build_runtime_resource(
                desktop_id="Agentbay",
                auth_code="test_auth",
                auth_context=None
            )
            
            assert runtime_resource.cloud_resource_id == expected_id, f"{env.value} 环境的 SessionService 配置不正确"
            print(f"   ✓ {env.value} 环境 SessionService 配置验证通过")
        
        # 恢复原始环境
        env_manager.switch_environment(original_env)
        print(f"\n✓ 已恢复到原始环境: {original_env.value}")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 环境切换测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 确保恢复原始环境
        try:
            env_manager.switch_environment(original_env)
        except:
            pass
        
        return False


def test_config_fallback():
    """测试配置回退机制"""
    print("\n=== 测试配置回退机制 ===")
    
    try:
        # 模拟配置项不存在的情况
        with patch.object(env_manager, 'get_config') as mock_config:
            # 模拟配置对象，但不包含 agentbay_cloud_resource_id
            mock_config_obj = Mock()
            # 模拟 _get_config_value 方法，当调用时返回默认值
            def mock_get_config_value(key, default=None):
                if key == 'agentbay_cloud_resource_id':
                    return default  # 返回默认值
                return None
            mock_config_obj._get_config_value.side_effect = mock_get_config_value
            mock_config.return_value = mock_config_obj
            
            session_service = SessionService()
            runtime_resource = session_service._build_runtime_resource(
                desktop_id="Agentbay",
                auth_code="test_auth",
                auth_context=None
            )
            
            # 应该使用默认值
            default_id = "akm-2f6c64e0-ba79-47cb-9847-d265773c7873"
            assert runtime_resource.cloud_resource_id == default_id, f"应该使用默认值 {default_id}"
            
            print(f"   ✓ 配置项不存在时正确使用默认值: {default_id}")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 配置回退测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始测试 AgentBay 云资源ID 配置功能...\n")
    
    test_functions = [
        test_agentbay_config_loading,
        test_session_service_agentbay_config,
        test_environment_switching,
        test_config_fallback,
    ]
    
    results = []
    
    for test_func in test_functions:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试函数 {test_func.__name__} 执行异常: {e}")
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 最终测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！AgentBay 配置功能正常。")
        print("\n✅ 功能实现总结:")
        print("   1. 配置文件中正确添加了 agentbay_cloud_resource_id 配置项")
        print("   2. 不同环境使用不同的 AgentBay 云资源ID")
        print("   3. SessionService 正确从配置中读取云资源ID")
        print("   4. 配置不存在时正确使用默认值")
        print("   5. 环境切换时配置正确更新")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
