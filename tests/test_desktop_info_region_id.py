#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 DesktopInfo 的 region_id 字段功能
"""

import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.domain.services.user_service import DesktopInfo, UserService
from src.domain.services.auth_service import AuthContext


def test_desktop_info_with_region_id():
    """测试 DesktopInfo 类的 region_id 字段"""
    print("=== 测试 DesktopInfo 类的 region_id 字段 ===")
    
    try:
        # 1. 测试创建带 region_id 的 DesktopInfo
        print("\n1. 测试创建带 region_id 的 DesktopInfo...")
        desktop_info = DesktopInfo(
            name="测试桌面",
            desktop_id="desktop_123",
            desktop_status="Running",
            region_id="cn-hangzhou"
        )
        
        print(f"   ✓ 桌面名称: {desktop_info.name}")
        print(f"   ✓ 桌面ID: {desktop_info.desktop_id}")
        print(f"   ✓ 桌面状态: {desktop_info.desktop_status}")
        print(f"   ✓ 区域ID: {desktop_info.region_id}")
        
        # 2. 测试 to_dict 方法
        print("\n2. 测试 to_dict 方法...")
        desktop_dict = desktop_info.to_dict()
        
        expected_keys = ["Name", "DesktopId", "DesktopStatus", "RegionId"]
        for key in expected_keys:
            assert key in desktop_dict, f"字典中缺少键: {key}"
            print(f"   ✓ {key}: {desktop_dict[key]}")
        
        # 3. 测试不提供 region_id 的情况
        print("\n3. 测试不提供 region_id 的情况...")
        desktop_info_no_region = DesktopInfo(
            name="无区域桌面",
            desktop_id="desktop_456",
            desktop_status="Stopped"
        )
        
        print(f"   ✓ 区域ID (默认): {desktop_info_no_region.region_id}")
        
        desktop_dict_no_region = desktop_info_no_region.to_dict()
        print(f"   ✓ 字典中的 RegionId: {desktop_dict_no_region['RegionId']}")
        
        return True
        
    except Exception as e:
        print(f"\n✗ DesktopInfo 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_user_service_desktop_parsing():
    """测试 UserService 解析桌面数据时获取 region_id"""
    print("\n=== 测试 UserService 解析桌面数据 ===")
    
    try:
        # 创建模拟的桌面数据
        mock_desktop_1 = Mock()
        mock_desktop_1.desktop_name = "桌面1"
        mock_desktop_1.desktop_id = "desktop_001"
        mock_desktop_1.desktop_status = "Running"
        mock_desktop_1.region_id = "cn-hangzhou"
        mock_desktop_1.management_flag = None  # 空值，应该被包含
        
        mock_desktop_2 = Mock()
        mock_desktop_2.desktop_name = "桌面2"
        mock_desktop_2.desktop_id = "desktop_002"
        mock_desktop_2.desktop_status = "Stopped"
        mock_desktop_2.region_id = "cn-beijing"
        mock_desktop_2.management_flag = "NoFlag"  # NoFlag，应该被包含
        
        mock_desktop_3 = Mock()
        mock_desktop_3.desktop_name = "管理桌面"
        mock_desktop_3.desktop_id = "desktop_003"
        mock_desktop_3.desktop_status = "Running"
        mock_desktop_3.region_id = "cn-shanghai"
        mock_desktop_3.management_flag = "ManagementFlag"  # 管理桌面，应该被过滤
        
        # 创建一个没有 region_id 属性的桌面
        mock_desktop_4 = Mock(spec=['desktop_name', 'desktop_id', 'desktop_status', 'management_flag'])
        mock_desktop_4.desktop_name = "无区域桌面"
        mock_desktop_4.desktop_id = "desktop_004"
        mock_desktop_4.desktop_status = "Running"
        mock_desktop_4.management_flag = None
        # 这个 Mock 没有 region_id 属性，getattr 会返回默认值 None
        
        # 创建模拟的响应
        mock_response_body = Mock()
        mock_response_body.data = [mock_desktop_1, mock_desktop_2, mock_desktop_3, mock_desktop_4]
        
        mock_response = Mock()
        mock_response.body = mock_response_body
        
        # 创建模拟的认证上下文
        mock_context = Mock(spec=AuthContext)
        mock_context.user_key = "test_user"
        mock_context.end_user_id = 12345
        
        # 创建 UserService 实例
        user_service = UserService()
        
        # 模拟 PC 客户端
        with patch.object(user_service, '_get_pc_client') as mock_get_pc_client:
            mock_pc_client = Mock()
            mock_pc_client.describe_desktops.return_value = mock_response
            mock_get_pc_client.return_value = mock_pc_client
            
            # 调用方法
            result = user_service.get_user_environments(mock_context)
            
            # 验证结果
            print(f"\n1. 验证环境数量...")
            # 应该有 3 个桌面（2个有效桌面 + 1个默认AgentBay）
            # desktop_3 应该被过滤掉因为 management_flag = "ManagementFlag"
            expected_count = 4  # desktop_1, desktop_2, desktop_4, AgentBay
            assert result.total == expected_count, f"期望 {expected_count} 个环境，实际 {result.total} 个"
            print(f"   ✓ 环境总数: {result.total}")
            
            print(f"\n2. 验证每个环境的详细信息...")
            for i, env in enumerate(result.environments):
                print(f"   环境 {i+1}:")
                print(f"     名称: {env.name}")
                print(f"     桌面ID: {env.desktop_id}")
                print(f"     状态: {env.desktop_status}")
                print(f"     区域ID: {env.region_id}")
                
                # 验证 to_dict 方法
                env_dict = env.to_dict()
                assert "RegionId" in env_dict, "环境字典中应该包含 RegionId"
                print(f"     字典形式: {env_dict}")
            
            # 验证特定的桌面
            print(f"\n3. 验证特定桌面的区域ID...")
            
            # 查找桌面1
            desktop_1_env = next((env for env in result.environments if env.desktop_id == "desktop_001"), None)
            assert desktop_1_env is not None, "应该找到 desktop_001"
            assert desktop_1_env.region_id == "cn-hangzhou", f"desktop_001 的区域ID应该是 cn-hangzhou，实际是 {desktop_1_env.region_id}"
            print(f"   ✓ desktop_001 区域ID: {desktop_1_env.region_id}")
            
            # 查找桌面2
            desktop_2_env = next((env for env in result.environments if env.desktop_id == "desktop_002"), None)
            assert desktop_2_env is not None, "应该找到 desktop_002"
            assert desktop_2_env.region_id == "cn-beijing", f"desktop_002 的区域ID应该是 cn-beijing，实际是 {desktop_2_env.region_id}"
            print(f"   ✓ desktop_002 区域ID: {desktop_2_env.region_id}")
            
            # 验证管理桌面被过滤
            desktop_3_env = next((env for env in result.environments if env.desktop_id == "desktop_003"), None)
            assert desktop_3_env is None, "desktop_003 应该被过滤掉"
            print(f"   ✓ desktop_003 (管理桌面) 已被正确过滤")
            
            # 查找无区域桌面
            desktop_4_env = next((env for env in result.environments if env.desktop_id == "desktop_004"), None)
            assert desktop_4_env is not None, "应该找到 desktop_004"
            assert desktop_4_env.region_id is None, f"desktop_004 的区域ID应该是 None，实际是 {desktop_4_env.region_id}"
            print(f"   ✓ desktop_004 区域ID (无区域): {desktop_4_env.region_id}")
            
            # 查找默认 AgentBay 环境
            agentbay_env = next((env for env in result.environments if env.desktop_id == "Agentbay"), None)
            assert agentbay_env is not None, "应该找到 Agentbay 环境"
            assert agentbay_env.region_id == "default", f"Agentbay 的区域ID应该是 default，实际是 {agentbay_env.region_id}"
            print(f"   ✓ Agentbay 区域ID: {agentbay_env.region_id}")
        
        return True
        
    except Exception as e:
        print(f"\n✗ UserService 桌面解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_management_flag_filtering():
    """测试 ManagementFlag 过滤逻辑"""
    print("\n=== 测试 ManagementFlag 过滤逻辑 ===")
    
    try:
        # 创建不同 management_flag 的桌面数据
        test_cases = [
            {"management_flag": None, "should_include": True, "description": "management_flag 为 None"},
            {"management_flag": "NoFlag", "should_include": True, "description": "management_flag 为 NoFlag"},
            {"management_flag": "", "should_include": True, "description": "management_flag 为空字符串"},
            {"management_flag": "ManagementFlag", "should_include": False, "description": "management_flag 为 ManagementFlag"},
            {"management_flag": "SomeOtherFlag", "should_include": False, "description": "management_flag 为其他值"},
        ]
        
        for i, case in enumerate(test_cases):
            print(f"\n{i+1}. 测试 {case['description']}...")
            
            # 创建模拟桌面
            mock_desktop = Mock()
            mock_desktop.desktop_name = f"测试桌面{i+1}"
            mock_desktop.desktop_id = f"desktop_{i+1:03d}"
            mock_desktop.desktop_status = "Running"
            mock_desktop.region_id = f"cn-region{i+1}"
            mock_desktop.management_flag = case["management_flag"]
            
            # 创建模拟响应
            mock_response_body = Mock()
            mock_response_body.data = [mock_desktop]
            
            mock_response = Mock()
            mock_response.body = mock_response_body
            
            # 创建模拟认证上下文
            mock_context = Mock(spec=AuthContext)
            mock_context.user_key = "test_user"
            mock_context.end_user_id = 12345
            
            # 创建 UserService 实例
            user_service = UserService()
            
            # 模拟 PC 客户端
            with patch.object(user_service, '_get_pc_client') as mock_get_pc_client:
                mock_pc_client = Mock()
                mock_pc_client.describe_desktops.return_value = mock_response
                mock_get_pc_client.return_value = mock_pc_client
                
                # 调用方法
                result = user_service.get_user_environments(mock_context)
                
                # 验证结果
                if case["should_include"]:
                    # 应该包含这个桌面 + AgentBay = 2个环境
                    assert result.total == 2, f"应该包含桌面，期望2个环境，实际{result.total}个"
                    
                    # 查找测试桌面
                    test_desktop = next((env for env in result.environments if env.desktop_id == mock_desktop.desktop_id), None)
                    assert test_desktop is not None, "应该找到测试桌面"
                    print(f"   ✓ 桌面被正确包含: {test_desktop.name} (region_id: {test_desktop.region_id})")
                else:
                    # 应该只有 AgentBay = 1个环境
                    assert result.total == 1, f"应该过滤桌面，期望1个环境，实际{result.total}个"
                    
                    # 确认没有测试桌面
                    test_desktop = next((env for env in result.environments if env.desktop_id == mock_desktop.desktop_id), None)
                    assert test_desktop is None, "测试桌面应该被过滤"
                    print(f"   ✓ 桌面被正确过滤")
        
        return True
        
    except Exception as e:
        print(f"\n✗ ManagementFlag 过滤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始测试 DesktopInfo 的 region_id 字段功能...\n")
    
    test_functions = [
        test_desktop_info_with_region_id,
        test_user_service_desktop_parsing,
        test_management_flag_filtering,
    ]
    
    results = []
    
    for test_func in test_functions:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试函数 {test_func.__name__} 执行异常: {e}")
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 最终测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！DesktopInfo 的 region_id 字段功能正常。")
        print("\n✅ 功能实现总结:")
        print("   1. DesktopInfo 类成功添加了 region_id 字段")
        print("   2. to_dict 方法正确包含 RegionId")
        print("   3. UserService 正确从桌面数据中提取 region_id")
        print("   4. ManagementFlag 过滤逻辑正常工作")
        print("   5. 默认 AgentBay 环境包含 region_id")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
