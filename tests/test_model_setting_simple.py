#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型设置功能简化测试
不依赖认证环境，专注测试核心逻辑
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


def test_model_setting_objects():
    """测试模型设置对象的创建和使用"""
    print("=== 模型设置对象测试 ===")
    
    try:
        from alibabacloud_wuyingaiinner20250708 import models as waiy_models
        
        # 1. 测试创建不同模型的设置对象
        test_models = [
            "qwen-plus",
            "qwen-max", 
            "qwen-turbo",
            "claude-3.5-sonnet",
            "gpt-4o",
            None,  # 测试 None 值
            "",    # 测试空字符串
        ]
        
        print("\n1. 测试模型设置对象创建...")
        for model in test_models:
            model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
                model_level=model
            )
            print(f"   ✓ model_level='{model}' -> {model_setting.model_level}")
        
        # 2. 测试模型设置对象的属性
        print("\n2. 测试模型设置对象属性...")
        model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
            model_level="qwen-max"
        )
        
        # 检查对象属性
        assert hasattr(model_setting, 'model_level'), "model_setting 应该有 model_level 属性"
        assert model_setting.model_level == "qwen-max", f"model_level 应该是 'qwen-max'，实际是 '{model_setting.model_level}'"
        
        print(f"   ✓ 对象属性验证成功: model_level={model_setting.model_level}")
        
        # 3. 测试对象方法
        print("\n3. 测试模型设置对象方法...")
        
        # 测试 to_map 方法
        if hasattr(model_setting, 'to_map'):
            map_result = model_setting.to_map()
            print(f"   ✓ to_map() 方法: {map_result}")
        
        # 测试 validate 方法
        if hasattr(model_setting, 'validate'):
            model_setting.validate()
            print(f"   ✓ validate() 方法执行成功")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 模型设置对象测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_user_setting_integration():
    """测试用户设置集成逻辑"""
    print("\n=== 用户设置集成测试 ===")
    
    try:
        from src.application.api_models import UserSettingData, CurrentSetting
        from alibabacloud_wuyingaiinner20250708 import models as waiy_models
        
        # 1. 创建用户设置数据
        print("\n1. 创建用户设置数据...")
        
        test_cases = [
            {"model": "qwen-plus", "desktop_id": "desktop_1"},
            {"model": "claude-3.5-sonnet", "desktop_id": "desktop_2"},
            {"model": None, "desktop_id": "desktop_3"},  # 未设置模型
            {"model": "", "desktop_id": "desktop_4"},    # 空模型
        ]
        
        for i, case in enumerate(test_cases):
            current_setting = CurrentSetting(
                desktop_id=case["desktop_id"],
                model=case["model"]
            )
            
            setting_data = UserSettingData(
                current_setting=current_setting,
                available_environments=[],
                available_models=["qwen-plus", "qwen-max", "claude-3.5-sonnet"],
                version="1.0.0"
            )
            
            print(f"   ✓ 测试用例 {i+1}: model='{case['model']}', desktop_id='{case['desktop_id']}'")
            
            # 2. 模拟 send_message 中的逻辑
            model_level = setting_data.current_setting.model
            model_setting = None
            
            if model_level:
                model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
                    model_level=model_level
                )
                print(f"      -> 创建模型设置: model_level='{model_setting.model_level}'")
            else:
                print(f"      -> 用户未设置模型，model_setting=None")
            
            # 验证逻辑
            if case["model"] and case["model"].strip():
                assert model_setting is not None, f"当 model='{case['model']}' 时，应该创建 model_setting"
                assert model_setting.model_level == case["model"], f"model_level 应该是 '{case['model']}'"
            else:
                assert model_setting is None, f"当 model='{case['model']}' 时，model_setting 应该是 None"
        
        return True
        
    except Exception as e:
        print(f"\n✗ 用户设置集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_message_context_creation():
    """测试消息上下文创建逻辑（不依赖认证）"""
    print("\n=== 消息上下文创建测试 ===")
    
    try:
        from alibabacloud_wuyingaiinner20250708 import models as waiy_models
        
        # 1. 测试 MessageAsyncRequestContext 创建
        print("\n1. 测试 MessageAsyncRequestContext 创建...")
        
        # 创建运行时资源
        runtime_resource = waiy_models.MessageAsyncRequestContextRuntimeResource(
            cloud_resource_id="test_desktop",
            token="test_auth_code",
            type="desktop",
            region="cn-hangzhou"
        )
        
        # 创建模型设置
        model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
            model_level="qwen-max"
        )
        
        # 创建消息上下文
        message_context = waiy_models.MessageAsyncRequestContext(
            runtime_resource=runtime_resource,
            session_id="test_session_123",
            user_id="12345",
            model_setting=model_setting
        )
        
        print(f"   ✓ 消息上下文创建成功")
        print(f"   ✓ session_id: {message_context.session_id}")
        print(f"   ✓ user_id: {message_context.user_id}")
        print(f"   ✓ model_setting.model_level: {message_context.model_setting.model_level}")
        print(f"   ✓ runtime_resource.cloud_resource_id: {message_context.runtime_resource.cloud_resource_id}")
        
        # 2. 测试不同的模型设置
        print("\n2. 测试不同的模型设置...")
        
        test_models = ["qwen-plus", "claude-3.5-sonnet", "gpt-4o", None]
        
        for model in test_models:
            if model:
                model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
                    model_level=model
                )
            else:
                model_setting = None
            
            context = waiy_models.MessageAsyncRequestContext(
                session_id="test_session",
                user_id="12345",
                model_setting=model_setting
            )
            
            if model_setting:
                print(f"   ✓ model='{model}' -> model_setting.model_level='{context.model_setting.model_level}'")
            else:
                print(f"   ✓ model=None -> model_setting=None")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 消息上下文创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_send_message_logic_simulation():
    """模拟 send_message 方法中的模型设置逻辑"""
    print("\n=== send_message 逻辑模拟测试 ===")
    
    try:
        from src.application.api_models import UserSettingData, CurrentSetting
        from alibabacloud_wuyingaiinner20250708 import models as waiy_models
        
        print("\n模拟 send_message 方法中的模型设置逻辑...")
        
        # 模拟用户设置数据
        current_setting = CurrentSetting(
            desktop_id="test_desktop_456",
            model="claude-3.5-sonnet"
        )
        
        setting_data = UserSettingData(
            current_setting=current_setting,
            available_environments=[],
            available_models=["qwen-plus", "qwen-max", "claude-3.5-sonnet"],
            version="1.0.0"
        )
        
        # 模拟 send_message 中的逻辑
        print("1. 获取用户设置...")
        model_level = setting_data.current_setting.model
        print(f"   ✓ 从用户设置获取模型: {model_level}")
        
        print("2. 创建模型设置对象...")
        model_setting = None
        if model_level:
            model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
                model_level=model_level
            )
            print(f"   ✓ 创建模型设置成功: model_level={model_setting.model_level}")
        else:
            print("   ✓ 用户未设置模型，使用默认配置")
        
        print("3. 创建消息上下文...")
        runtime_resource = waiy_models.MessageAsyncRequestContextRuntimeResource(
            desktop_id="test_desktop",
            auth_code="test_auth"
        )
        
        message_context = waiy_models.MessageAsyncRequestContext(
            runtime_resource=runtime_resource,
            session_id="test_session_789",
            user_id="67890",
            model_setting=model_setting
        )
        
        print(f"   ✓ 消息上下文创建成功")
        print(f"   ✓ 验证模型设置传递: {message_context.model_setting.model_level}")
        
        # 验证结果
        assert message_context.model_setting is not None, "model_setting 不应该为 None"
        assert message_context.model_setting.model_level == "claude-3.5-sonnet", "模型级别不匹配"
        
        print("4. 验证完成 ✓")
        
        return True
        
    except Exception as e:
        print(f"\n✗ send_message 逻辑模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始模型设置功能简化测试...\n")
    
    test_functions = [
        test_model_setting_objects,
        test_user_setting_integration,
        test_message_context_creation,
        test_send_message_logic_simulation,
    ]
    
    results = []
    
    for test_func in test_functions:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试函数 {test_func.__name__} 执行异常: {e}")
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 最终测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有简化测试通过！模型设置功能核心逻辑正确。")
        print("\n✅ 功能实现总结:")
        print("   1. MessageAsyncRequestContextModelSetting 对象创建正常")
        print("   2. 用户设置数据获取和处理正常")
        print("   3. 模型设置对象传递给消息上下文正常")
        print("   4. send_message 方法逻辑模拟正常")
        return True
    else:
        print("❌ 部分简化测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
