#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户业务服务
负责用户相关的业务逻辑，包括获取用户执行环境等
"""
from typing import List, Dict, Any, Optional
from loguru import logger

from .auth_service import AuthContext
from ...popclients.pc_inside_client import get_pc_inside_client, PcInsideClientError
from ...shared.config.environments import env_manager
from ...infrastructure.database.repositories.user_setting_repository import user_setting_db_service
from ...infrastructure.database.models.user_setting_models import UserSettingModel
from ...application.api_models import UserSettingData, CurrentSetting


class DesktopInfo:
    """桌面信息模型"""
    
    def __init__(self, name: str, desktop_id: str, desktop_status: str = "Unknown"):
        self.name = name
        self.desktop_id = desktop_id
        self.desktop_status = desktop_status
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "Name": self.name,
            "DesktopId": self.desktop_id,
            "DesktopStatus": self.desktop_status
        }


class EnvironmentListResult:
    """执行环境列表结果"""
    
    def __init__(self, environments: List[DesktopInfo], total: int):
        self.environments = environments
        self.total = total
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "Environments": [env.to_dict() for env in self.environments],
            "Total": self.total
        }


class UserService:
    """用户业务服务 - 处理用户相关的业务逻辑"""
    
    def __init__(self):
        self._pc_client = None
    
    def _get_pc_client(self):
        """获取PC内部服务客户端"""
        if self._pc_client is None:
            try:
                # 使用无AK认证方式初始化客户端
                config = env_manager.get_config()

                # 获取端点配置（如果有的话）
                endpoint = getattr(config, 'pc_endpoint', None)

                self._pc_client = get_pc_inside_client(endpoint=endpoint)
                logger.info("[UserService] PC内部服务客户端初始化成功（使用无AK认证）")

            except Exception as e:
                logger.error(f"[UserService] PC内部服务客户端初始化失败: {e}")
                raise

        return self._pc_client
    
    def get_user_environments(self, context: AuthContext) -> EnvironmentListResult:
        """
        获取用户执行环境列表
        
        Args:
            context: 认证上下文
            
        Returns:
            EnvironmentListResult: 执行环境列表结果
        """
        try:
            logger.info(f"[UserService] 获取用户执行环境: user={context.user_key}")
            
            environments = []

            # 获取PC内部服务客户端
            pc_client = self._get_pc_client()

            # 调用describe_desktops接口，使用ali_uid作为end_user_id
            try:
                response = pc_client.describe_desktops(
                    end_user_id=str(context.end_user_id)
                )

                # 解析响应
                if response and hasattr(response, 'body') and response.body:
                    response_body = response.body

                    # 获取桌面列表
                    if hasattr(response_body, 'desktops') and response_body.desktops:
                        for desktop in response_body.desktops:
                            # 构建桌面信息
                            desktop_name = getattr(desktop, 'desktop_name', f"桌面-{getattr(desktop, 'desktop_id', 'unknown')}")
                            desktop_id = getattr(desktop, 'desktop_id', '')
                            desktop_status = getattr(desktop, 'desktop_status', 'Unknown')

                            if desktop_id:  # 只添加有效的桌面
                                desktop_info = DesktopInfo(
                                    name=desktop_name,
                                    desktop_id=desktop_id,
                                    desktop_status=desktop_status
                                )
                                environments.append(desktop_info)
                                logger.debug(f"[UserService] 找到桌面: {desktop_name} ({desktop_id})")

                    logger.info(f"[UserService] 从PC服务获取到 {len(environments)} 个桌面")

                else:
                    logger.warning(f"[UserService] PC服务返回空响应")

            except PcInsideClientError as e:
                logger.error(f"[UserService] 调用PC服务失败: {e}")
                # 不抛出异常，继续添加默认环境
            except Exception as e:
                logger.error(f"[UserService] PC服务调用异常: {e}")
                # 不抛出异常，继续添加默认环境

            # 始终添加默认的AgentBay环境
            agentbay_env = DesktopInfo(
                name="Agentbay",
                desktop_id="Agentbay",
                desktop_status="Running"
            )
            environments.append(agentbay_env)
            logger.debug("[UserService] 添加默认AgentBay环境")

            # 构建结果
            result = EnvironmentListResult(
                environments=environments,
                total=len(environments)
            )

            logger.info(f"[UserService] 用户环境获取成功: user={context.user_key}, 总数={result.total}")
            return result
            
        except Exception as e:
            logger.error(f"[UserService] 获取用户执行环境失败: user={context.user_key}, error={e}")
            # 返回最基本的环境列表
            agentbay_env = DesktopInfo(
                name="Agentbay",
                desktop_id="Agentbay", 
                desktop_status="Running"
            )
            return EnvironmentListResult(
                environments=[agentbay_env],
                total=1
            )
    
    def update_user_setting(
        self,
        context: AuthContext,
        desktop_id: Optional[str] = None,
        model: Optional[str] = None
    ) -> UserSettingData:
        """
        更新用户设置
        
        Args:
            context: 认证上下文
            desktop_id: 优先使用的桌面ID
            model: 优先使用的模型
            
        Returns:
            UserSettingData: 更新后的用户设置数据
        """
        try:
            logger.info(f"[UserService] 更新用户设置: user={context.user_key}, ali_uid={context.ali_uid}, "
                       f"desktop_id={desktop_id}, model={model}")
            
            # 创建或更新用户设置
            setting_model = user_setting_db_service.create_or_update_user_setting(
                ali_uid=context.ali_uid,
                wy_id=context.wy_id,
                desktop_id=desktop_id,
                model=model
            )
            
            # 获取可用环境和模型列表
            environments_result = self.get_user_environments(context)
            available_environments = [env.to_dict() for env in environments_result.environments]
            available_models = self._get_available_models()
            version = self._get_version()
            
            # 构建响应数据
            current_setting = CurrentSetting(
                desktop_id=setting_model.desktop_id,
                model=setting_model.model
            )

            setting_data = UserSettingData(
                current_setting=current_setting,
                available_environments=available_environments,
                available_models=available_models,
                version=version
            )
            
            logger.info(f"[UserService] 用户设置更新成功: user={context.user_key}")
            return setting_data
            
        except Exception as e:
            logger.error(f"[UserService] 更新用户设置失败: user={context.user_key}, error={e}")
            raise
    
    def get_user_setting(self, context: AuthContext) -> UserSettingData:
        """
        获取用户设置
        Args:
            context: 认证上下文
        Returns:
            UserSettingData: 用户设置数据
        """
        try:
            logger.info(f"[UserService] 获取用户设置: user={context.user_key}, ali_uid={context.ali_uid}")
            
            # 获取用户设置
            setting_model = user_setting_db_service.get_user_setting(
                ali_uid=context.ali_uid,
                wy_id=context.wy_id
            )
            
            # 获取可用环境和模型列表
            environments_result = self.get_user_environments(context)
            available_environments = [env.to_dict() for env in environments_result.environments]
            available_models = self._get_available_models()
            version = self._get_version()
            
            current_setting = CurrentSetting(
                desktop_id=setting_model.desktop_id if setting_model else None,
                model=setting_model.model if setting_model else None
            )

            setting_data = UserSettingData(
                current_setting=current_setting,
                available_environments=available_environments,
                available_models=available_models,
                version=version
            )
            
            logger.info(f"[UserService] 用户设置获取成功: user={context.user_key}")
            return setting_data
            
        except Exception as e:
            logger.error(f"[UserService] 获取用户设置失败: user={context.user_key}, error={e}")
            raise
    
    def _get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        # 暂时返回固定的模型列表
        return [
            "balance",
            "high-performance"
        ]
    
    def _get_version(self) -> str:
        """获取版本信息"""
        # 暂时返回固定版本
        return "1.0.0"


# 创建全局实例
user_service = UserService()